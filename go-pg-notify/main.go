package main

import (
	"context"
	"demo/app"
	"demo/config"
	"demo/database"
	"demo/database/migrations"
	"demo/http/handlers"
	"demo/http/server"
	"demo/pubsub"
	"fmt"
	"log"
	"log/slog"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/ThreeDotsLabs/watermill"
	"github.com/ThreeDotsLabs/watermill/message"
	"github.com/ThreeDotsLabs/watermill/message/router/middleware"
	"github.com/ThreeDotsLabs/watermill/message/router/plugin"
	"github.com/uptrace/bun"
	"github.com/uptrace/bun/migrate"
)

// ---- Main ----
func main() {
	var msgs chan *message.Message
	msgs = make(chan *message.Message, 1)
	conf := config.New()
	mux := http.NewServeMux()
	srv := server.New(mux)
	db := database.New()

	// Set up context with cancellation for graceful shutdown
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// Set connection pool parameters
	db.SetMaxOpenConns(10)
	db.SetMaxIdleConns(5)
	db.SetConnMaxLifetime(5 * time.Minute)

	waitForDB(ctx, db)

	err := initDatabaseMigration(ctx, db)
	if err != nil {
		log.Fatalf("Failed to migrate database: %v", err)
	}

	// Set up Publisher and Subscriber
	publisher := pubsub.NewPostgresPublisher(db)
	subscriber := pubsub.NewPostgresSubscriber(db,
		pubsub.WithConsumerGroup("app-group-1"),
		pubsub.WithInstanceID("worker-"+os.Getenv("POD_NAME")),
		pubsub.WithConsumerGroupsEnabled(true), // Override and disable

	)

	router := initRouter(conf, subscriber, publisher)

	// just for debug, we are printing all messages received on `incoming_messages_topic`
	_ = router.AddNoPublisherHandler(
		"print_incoming_messages",
		conf.Topic,
		subscriber,
		func(msg *message.Message) error {
			msgs <- msg
			return printMessages(msg)
		},
	)

	go func() {
		if err := router.Run(ctx); err != nil {
			panic(err)
		}
	}()

	app := app.App{
		Server:     srv,
		Router:     mux,
		Config:     &conf,
		DB:         db,
		Publisher:  publisher,
		Subscriber: subscriber,
	}

	// Create Bun DB instance

	defer db.Close()

	// Set up signal handling for graceful shutdown
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	defer func() {
		if err := srv.Stop(ctx); err != nil {
			log.Printf("Error closing server: %v", err)
		}
	}()
	defer publisher.Close()
	defer subscriber.Close()

	// Start subscriber with a longer timeout
	// subCtx, subCancel := context.WithTimeout(ctx, 30*time.Second)
	// defer subCancel()

	// msgs, err := subscriber.Subscribe(context.Background(), conf.Topic)
	// if err != nil {
	// 	log.Fatalf("Failed to subscribe: %v", err)
	// }

	log.Printf("Subscribed to topic: %s\n", conf.Topic)

	// // Process received messages
	// go func() {
	// 	for msg := range msgs {
	// 		fmt.Printf("🔔 Received message:\n")
	// 		fmt.Printf("  UUID: %s\n", msg.UUID)
	// 		fmt.Printf("  Payload: %s\n", string(msg.Payload))
	// 		fmt.Printf("  Metadata: %v\n", msg.Metadata)
	// 	}
	// 	log.Println("Message processing stopped")
	// }()

	handlers.RegisterHandlers(ctx, app, msgs)
	go func() {
		if err := srv.Start(); err != nil {
			log.Printf("Server error: %v", err)
		}
	}()

	<-router.Running()

	// Wait for the server to be ready
	<-srv.Ready()
	log.Println("Server is ready to accept connections")

	// Wait for termination signal
	sig := <-sigChan
	cancel() // Cancel the context to initiate graceful shutdown
	log.Printf("Received signal: %v, shutting down\n", sig)

	// Allow time for cleanup
	log.Println("Shutting down...")
	log.Println("Shutdown complete")
}

func waitForDB(ctx context.Context, db *bun.DB) {
	// Verify database connection with retry
	maxRetries := 3
	var err error
	for i := 0; i < maxRetries; i++ {
		ctxTimeout, cancelTimeout := context.WithTimeout(ctx, 5*time.Second)
		err = db.PingContext(ctxTimeout)
		cancelTimeout()

		if err == nil {
			log.Printf("Successfully connected to the database on attempt %d", i+1)
			break
		}

		if i < maxRetries-1 {
			log.Printf("Database connection attempt %d failed: %v. Retrying...", i+1, err)
			time.Sleep(2 * time.Second)
		}
	}

	if err != nil {
		log.Fatalf("Failed to connect to database after %d attempts: %v", maxRetries, err)
	}
}

func initRouter(config config.Config, sub message.Subscriber, pub message.Publisher) *message.Router {
	logger := watermill.NewSlogLogger(slog.Default())

	router, err := message.NewRouter(message.RouterConfig{
		CloseTimeout: time.Second * 30,
	}, logger)
	if err != nil {
		panic(err)
	}

	// SignalsHandler will gracefully shutdown Router when SIGTERM is received.
	// You can also close the router by just calling `r.Close()`.
	router.AddPlugin(plugin.SignalsHandler)

	// Router level middleware are executed for every message sent to the router
	router.AddMiddleware(
		// CorrelationID will copy the correlation id from the incoming message's metadata to the produced messages
		middleware.CorrelationID,

		// The handler function is retried if it returns an error.
		// After MaxRetries, the message is Nacked and it's up to the PubSub to resend it.
		middleware.Retry{
			MaxRetries:      3,
			InitialInterval: time.Millisecond * 100,
			Logger:          logger,
		}.Middleware,

		// Recoverer handles panics from handlers.
		// In this case, it passes them as errors to the Retry middleware.
		middleware.Recoverer,
	)

	return router
}

func printMessages(msg *message.Message) error {
	fmt.Printf(
		"\n> Received message: %s\n> %s\n> metadata: %v\n\n",
		msg.UUID, string(msg.Payload), msg.Metadata,
	)
	return nil
}

func initDatabaseMigration(ctx context.Context, db *bun.DB) error {

	migrations := migrations.Migrations

	migrator := migrate.NewMigrator(db, migrations)
	migrator.Init(ctx)

	group, err := migrator.Migrate(ctx)
	if err != nil {
		return err
	}

	if group.ID == 0 {
		fmt.Printf("there are no new migrations to run\n")
		return nil
	}

	fmt.Printf("migrated to %s\n", group)

	return nil
}
