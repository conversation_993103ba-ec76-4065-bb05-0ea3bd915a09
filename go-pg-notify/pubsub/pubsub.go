package pubsub

import (
	"context"
	"demo/model"
	"encoding/json"
	"fmt"
	"log"
	"log/slog"
	"slices"
	"sync"
	"time"

	"github.com/ThreeDotsLabs/watermill"
	"github.com/ThreeDotsLabs/watermill/message"
	"github.com/uptrace/bun"
	"github.com/uptrace/bun/driver/pgdriver"
)

type PersistMessageFunc func(ctx context.Context, tx bun.Tx, topic string, message *message.Message) (string, error)

type PostgresNotification struct {
	ID       string            `json:"id"`
	Payload  string            `json:"payload"`
	Metadata map[string]string `json:"metadata"`
}

// PostgresPublisher publishes messages using PostgreSQL NOTIFY
type PostgresPublisher struct {
	db             *bun.DB
	ctx            context.Context
	PersistMessage PersistMessageFunc
}

// NewPostgresPublisher creates a new PostgreSQL publisher using Bun
func NewPostgresPublisher(db *bun.DB) *PostgresPublisher {
	ctx := context.Background()
	return &PostgresPublisher{
		db:             db,
		ctx:            ctx,
		PersistMessage: DefaultPersistMessage,
	}
}

// Publish sends messages to the specified topic
func (p *PostgresPublisher) Publish(topic string, messages ...*message.Message) error {

	for _, msg := range messages {
		if err := p.publishSingle(p.ctx, topic, msg); err != nil {
			return err
		}
	}
	return nil
}

// Helper method to publish a single message
func (p *PostgresPublisher) publishSingle(ctx context.Context, topic string, msg *message.Message) error {
	tx, err := p.db.BeginTx(ctx, nil)
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %w", err)
	}

	// Use a closure to ensure proper rollback handling
	defer func() {
		// Only rollback if not already committed
		_ = tx.Rollback()
	}()

	payload, err := p.PersistMessage(ctx, tx, topic, msg)
	if err != nil {
		return fmt.Errorf("failed to create message record: %w", err)
	}

	// PostgreSQL NOTIFY has a limit of 8000 bytes for payload
	if len(payload) > 8000 {
		return fmt.Errorf("payload size exceeds PostgreSQL NOTIFY limit of 8000 bytes: %d bytes", len(payload))
	}

	if err := pgdriver.Notify(ctx, p.db, topic, payload); err != nil {
		return fmt.Errorf("failed to publish message: %w", err)
	}

	if err := tx.Commit(); err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	msg.Ack()
	return nil
}

// Close closes the publisher's database connection
func (p *PostgresPublisher) Close() error {
	return nil // No need to close anything since we're using the shared db connection
}

// PostgresSubscriber subscribes to messages using PostgreSQL LISTEN
type PostgresSubscriber struct {
	db                *bun.DB
	ln                *pgdriver.Listener
	channelSize       int
	cancelFuncs       []context.CancelFunc
	mu                sync.Mutex // To protect the cancelFuncs slice
	consumerGroup     string     // Consumer group identifier
	instanceID        string     // Unique identifier for this instance
	useConsumerGroups bool       // Flag to enable/disable consumer groups
}

// NewPostgresSubscriber creates a new PostgreSQL subscriber
func NewPostgresSubscriber(db *bun.DB, options ...SubscriberOption) *PostgresSubscriber {
	subscriber := &PostgresSubscriber{
		db:                db,
		ln:                pgdriver.NewListener(db),
		channelSize:       100,
		consumerGroup:     "",
		instanceID:        watermill.NewUUID(),
		useConsumerGroups: false, // Disabled by default for backward compatibility
	}

	for _, option := range options {
		option(subscriber)
	}

	return subscriber
}

// Subscribe listens for messages on the specified topic
func (s *PostgresSubscriber) Subscribe(ctx context.Context, topic string) (<-chan *message.Message, error) {
	subCtx, cancel := context.WithCancel(ctx)

	if err := s.ln.Listen(ctx, topic); err != nil {
		cancel()
		return nil, fmt.Errorf("failed to listen on topic %s: %w", topic, err)
	}

	msgChan := make(chan *message.Message, s.channelSize)

	s.mu.Lock()
	s.cancelFuncs = append(s.cancelFuncs, cancel)
	s.mu.Unlock()

	// Log configuration for debugging
	if s.useConsumerGroups {
		slog.Info("Starting subscriber with consumer groups",
			"topic", topic,
			"group", s.consumerGroup,
			"instance", s.instanceID)
	} else {
		slog.Info("Starting subscriber without consumer groups", "topic", topic)
	}

	notifCh := s.ln.Channel(
		pgdriver.WithChannelSize(s.channelSize),
		pgdriver.WithChannelOverflowHandler(func(n pgdriver.Notification) {
			slog.Error("Channel overflow", "topic", topic, "payload", n.Payload)
		}),
	)

	go func() {
		defer close(msgChan)
		for n := range notifCh {
			notification, err := postgresNotificationFromPayload(n.Payload)
			if err != nil {
				slog.Error("Failed to parse notification", "error", err)
				continue
			}

			// Skip consumer group logic if disabled
			if !s.useConsumerGroups {
				msg := buildMessageFromNotification(notification, topic, n.Channel)
				select {
				case <-subCtx.Done():
					return
				case msgChan <- msg:
					slog.Info("Received message in subscriber", "topic", topic, "messageID", notification.ID)
				}
				continue
			}

			// Consumer group logic
			claimed, err := s.claimMessage(subCtx, notification.ID, s.consumerGroup, s.instanceID)
			if err != nil {
				slog.Error("Failed to claim message", "error", err, "messageID", notification.ID)
				continue
			}

			// Only process if we successfully claimed the message
			if claimed {
				msg := buildMessageFromNotification(notification, topic, n.Channel)
				select {
				case <-subCtx.Done():
					return
				case msgChan <- msg:
					slog.Info("Processing message", "topic", topic, "messageID", notification.ID,
						"group", s.consumerGroup, "instance", s.instanceID)
				}
			} else {
				slog.Debug("Message claimed by another instance",
					"messageID", notification.ID, "group", s.consumerGroup)
			}
		}
	}()

	return msgChan, nil
}

// Close closes the subscriber's connection and cancels all goroutines
func (s *PostgresSubscriber) Close() error {
	s.mu.Lock()
	// Reverse the slice in place
	slices.Reverse(s.cancelFuncs)
	for _, cancel := range s.cancelFuncs {
		cancel()
	}
	s.cancelFuncs = nil
	s.mu.Unlock()

	return s.ln.Close()
}

func DefaultPersistMessage(ctx context.Context, tx bun.Tx, topic string, message *message.Message) (string, error) {
	payload := ""
	msg := &model.Message{
		ID:        message.UUID,
		Topic:     topic,
		Payload:   []byte(message.Payload),
		Status:    model.StatusPending,
		Priority:  1,
		Metadata:  map[string]string{},
		CreatedAt: time.Now(),
	}
	for key, value := range message.Metadata {
		msg.Metadata[key] = value
	}
	if _, err := tx.NewInsert().Model(msg).Exec(ctx); err != nil {
		log.Printf("Failed to queue message: %v", err)
		return payload, err
	}
	notification := PostgresNotification{
		ID:       msg.ID,
		Payload:  string(msg.Payload),
		Metadata: msg.Metadata,
	}
	jsonData, err := json.Marshal(notification)
	if err != nil {
		log.Printf("Failed to marshal notification: %v", err)
		return payload, err
	}
	payload = string(jsonData)
	return payload, nil
}

func getMessageRecord(ctx context.Context, db *bun.DB, id string) (*model.Message, error) {
	msg := &model.Message{}
	if err := db.NewSelect().Model(msg).Where("id = ?", id).Scan(ctx); err != nil {
		return nil, err
	}
	return msg, nil
}

func postgresNotificationFromPayload(payload string) (*PostgresNotification, error) {
	var notification PostgresNotification
	if err := json.Unmarshal([]byte(payload), &notification); err != nil {
		return nil, err
	}
	return &notification, nil
}

// Add option pattern for configuration
type SubscriberOption func(*PostgresSubscriber)

func WithConsumerGroup(groupID string) SubscriberOption {
	return func(s *PostgresSubscriber) {
		s.consumerGroup = groupID
		s.useConsumerGroups = true // Automatically enable when group is set
	}
}

// Explicit option to enable/disable consumer groups
func WithConsumerGroupsEnabled(enabled bool) SubscriberOption {
	return func(s *PostgresSubscriber) {
		s.useConsumerGroups = enabled
	}
}

func WithChannelSize(size int) SubscriberOption {
	return func(s *PostgresSubscriber) {
		s.channelSize = size
	}
}

// Option to set a custom instance ID
func WithInstanceID(id string) SubscriberOption {
	return func(s *PostgresSubscriber) {
		s.instanceID = id
	}
}

// Helper function to build message from notification
func buildMessageFromNotification(notification *PostgresNotification, topic, channel string) *message.Message {
	msg := message.NewMessage(notification.ID, []byte(notification.Payload))
	for key, value := range notification.Metadata {
		msg.Metadata.Set(key, value)
	}
	msg.Metadata.Set("topic", topic)
	msg.Metadata.Set("channel", channel)
	return msg
}

// claimMessage attempts to claim a message for processing by this instance
func (s *PostgresSubscriber) claimMessage(ctx context.Context, messageID, groupID, instanceID string) (bool, error) {
	// Use a transaction to ensure atomicity
	tx, err := s.db.BeginTx(ctx, nil)
	if err != nil {
		return false, fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback()

	// Try to update the message status with this instance ID
	// This uses a conditional update that only succeeds if the message isn't already claimed
	result, err := tx.NewUpdate().
		Model((*model.Message)(nil)).
		Set("consumer_group = ?", groupID).
		Set("consumer_instance = ?", instanceID).
		Set("claimed_at = ?", time.Now()).
		Where("id = ? AND (consumer_instance IS NULL OR consumer_instance = ?)", messageID, instanceID).
		Exec(ctx)

	if err != nil {
		return false, fmt.Errorf("failed to claim message: %w", err)
	}

	// Check if we successfully claimed the message
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return false, fmt.Errorf("failed to get rows affected: %w", err)
	}

	if err := tx.Commit(); err != nil {
		return false, fmt.Errorf("failed to commit transaction: %w", err)
	}

	return rowsAffected > 0, nil
}
